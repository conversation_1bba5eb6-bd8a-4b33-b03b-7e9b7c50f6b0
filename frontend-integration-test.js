/**
 * Frontend Integration Test Script
 * 在浏览器控制台中运行此脚本来验证前端集成
 */

(function() {
    'use strict';
    
    console.log('=== XWiki Watermark Frontend Integration Test ===');
    
    // 测试配置
    const testConfig = {
        baseUrl: window.location.origin,
        wikiName: 'xwiki',
        timeout: 5000
    };
    
    // 构建API基础URL
    const apiBase = testConfig.baseUrl + '/rest/wikis/' + testConfig.wikiName + '/watermark';
    console.log('API Base URL:', apiBase);
    
    // 测试函数
    function testRestEndpoint(endpoint, description) {
        return new Promise((resolve, reject) => {
            console.log(`\n--- Testing ${description} ---`);
            const url = apiBase + endpoint;
            console.log('URL:', url);
            
            const xhr = new XMLHttpRequest();
            xhr.open('GET', url, true);
            xhr.setRequestHeader('Accept', 'application/json');
            xhr.timeout = testConfig.timeout;
            
            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    if (xhr.status === 200) {
                        try {
                            const response = JSON.parse(xhr.responseText);
                            console.log('✅ SUCCESS - HTTP 200');
                            console.log('Response:', response);
                            resolve(response);
                        } catch (e) {
                            console.log('❌ FAILED - Invalid JSON:', e.message);
                            reject(e);
                        }
                    } else {
                        console.log(`❌ FAILED - HTTP ${xhr.status}: ${xhr.statusText}`);
                        reject(new Error(`HTTP ${xhr.status}`));
                    }
                }
            };
            
            xhr.onerror = function() {
                console.log('❌ FAILED - Network error');
                reject(new Error('Network error'));
            };
            
            xhr.ontimeout = function() {
                console.log('❌ FAILED - Request timeout');
                reject(new Error('Timeout'));
            };
            
            xhr.send();
        });
    }
    
    // 检查XWikiWatermark实例
    function checkWatermarkInstance() {
        console.log('\n--- Checking XWikiWatermark Instance ---');
        if (window.XWikiWatermark) {
            console.log('✅ XWikiWatermark instance found');
            console.log('Config:', window.XWikiWatermark.config);
            console.log('Initialized:', window.XWikiWatermark.isInitialized);
            return true;
        } else {
            console.log('❌ XWikiWatermark instance not found');
            return false;
        }
    }
    
    // 检查Canvas元素
    function checkCanvasElement() {
        console.log('\n--- Checking Canvas Element ---');
        const canvas = document.getElementById('xwiki-watermark-canvas');
        if (canvas) {
            console.log('✅ Watermark canvas found');
            console.log('Canvas size:', canvas.width + 'x' + canvas.height);
            console.log('Canvas style:', canvas.style.cssText);
            return true;
        } else {
            console.log('❌ Watermark canvas not found');
            return false;
        }
    }
    
    // 运行完整测试
    async function runFullTest() {
        try {
            // 1. 检查实例
            checkWatermarkInstance();
            
            // 2. 测试REST端点
            const statusResponse = await testRestEndpoint('/status', 'Status Check');
            const configResponse = await testRestEndpoint('/config', 'Configuration');
            const userResponse = await testRestEndpoint('/user', 'User Information');
            const textResponse = await testRestEndpoint('/text', 'Resolved Text');
            
            // 3. 检查Canvas
            checkCanvasElement();
            
            // 4. 总结
            console.log('\n=== Test Summary ===');
            console.log('✅ All REST endpoints working');
            console.log('✅ Frontend integration successful');
            
            if (statusResponse.shouldApply) {
                console.log('✅ Watermark should be applied');
            } else {
                console.log('ℹ️ Watermark disabled by configuration');
            }
            
        } catch (error) {
            console.log('\n=== Test Failed ===');
            console.log('❌ Error:', error.message);
        }
    }
    
    // 启动测试
    runFullTest();
    
})();
