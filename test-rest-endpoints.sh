#!/bin/bash

# XWiki Watermark Extension REST API Test Script
# This script tests all 4 REST endpoints to verify the fix

XWIKI_BASE_URL="http://localhost:8080"
WIKI_NAME="xwiki"
API_BASE="${XWIKI_BASE_URL}/rest/wikis/${WIKI_NAME}/watermark"

echo "=== XWiki Watermark Extension REST API Test ==="
echo "Base URL: ${API_BASE}"
echo ""

# Function to test an endpoint
test_endpoint() {
    local endpoint=$1
    local description=$2
    
    echo "Testing ${description}..."
    echo "URL: ${API_BASE}${endpoint}"
    
    response=$(curl -s -w "\nHTTP_CODE:%{http_code}" "${API_BASE}${endpoint}")
    http_code=$(echo "$response" | tail -n1 | cut -d: -f2)
    body=$(echo "$response" | head -n -1)
    
    echo "HTTP Code: $http_code"
    echo "Response: $body"
    
    if [ "$http_code" = "200" ]; then
        echo "✅ SUCCESS"
    else
        echo "❌ FAILED"
    fi
    echo "----------------------------------------"
}

# Test all endpoints
test_endpoint "/status" "Watermark Status Check"
test_endpoint "/config" "Watermark Configuration"
test_endpoint "/user" "Current User Information"
test_endpoint "/text" "Resolved Watermark Text"

echo ""
echo "=== Test Summary ==="
echo "If all endpoints return HTTP 200 with valid JSON, the fix is successful!"
echo "If you see HTTP 500 errors, there may still be dependency injection issues."
