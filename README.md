# XWiki 水印扩展 (XWiki Watermark Extension)

一个为 XWiki 设计的专业水印扩展，支持动态文本水印、占位符渲染、防复制功能和移动端兼容性。

## 🌟 主要特性

- **动态水印渲染**：基于 Canvas 的高质量水印生成
- **占位符支持**：支持 `${user}` 和 `${timestamp}` 动态替换
- **灵活配置**：8个可配置参数，满足不同需求
- **防复制保护**：可选的内容保护功能
- **移动端适配**：响应式设计，自动适配不同设备
- **管理面板集成**：集成到 XWiki 标准管理界面
- **多语言支持**：支持中文和英文界面
- **高性能**：Canvas 缓存和懒加载优化
- **线程安全**：支持并发访问和配置管理

## 📋 系统要求

- **XWiki**: 15.10.11+ (推荐 17.4.3+)
- **Java**: 17+
- **Maven**: 3.6+（仅开发时需要）

## 🚀 安装步骤

### 1. 下载扩展
从 [Releases](../../releases) 页面下载最新的 `xwiki-watermark-x.x.x.jar` 文件。

### 2. 安装到 XWiki
```bash
# 方法1：复制到 XWiki 扩展目录
cp xwiki-watermark-1.0-SNAPSHOT.jar /path/to/xwiki/WEB-INF/lib/

# 方法2：使用 XWiki Extension Manager
# 在 XWiki 管理面板中选择 "Extensions" -> "Install" -> 上传 JAR 文件
```

### 3. 重启 XWiki
重启 XWiki 服务器以加载新扩展。

### 4. 验证安装
访问 XWiki 管理面板，在 "Look & Feel" 分类下应该能看到 "Watermark Configuration" 选项。

## ⚙️ 配置说明

### 访问配置界面
1. 以管理员身份登录 XWiki
2. 进入 **管理面板** (Administration)
3. 选择 **Look & Feel** 分类
4. 点击 **Watermark Configuration**

### 配置参数详解

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| **启用水印** | 布尔 | `false` | 全局启用或禁用水印显示 |
| **文本模板** | 字符串 | `${user} - ${timestamp}` | 水印文本，支持占位符 |
| **水平间距** | 整数 | `200` | 水印间的水平距离（像素） |
| **垂直间距** | 整数 | `100` | 水印间的垂直距离（像素） |
| **旋转角度** | 整数 | `-30` | 水印旋转角度（-180° 到 180°） |
| **透明度** | 浮点 | `0.1` | 水印透明度（0.0-1.0） |
| **字体大小** | 整数 | `14` | 水印字体大小（像素） |
| **防复制保护** | 布尔 | `false` | 禁用文本选择和右键菜单 |
| **移动端应用** | 布尔 | `true` | 在移动设备上显示水印 |

### 占位符说明
- `${user}`: 当前用户名（未登录时显示 "Guest"）
- `${timestamp}`: 当前时间戳（格式：yyyy-MM-dd HH:mm:ss）

### 配置示例
```
启用水印: ✓
文本模板: "机密文档 - ${user} - ${timestamp}"
水平间距: 250px
垂直间距: 120px
旋转角度: -45°
透明度: 0.15
字体大小: 16px
防复制保护: ✓
移动端应用: ✓
```

## 📱 使用方法

### 基本使用
1. 在管理面板中启用水印
2. 配置水印文本和样式参数
3. 保存配置
4. 访问任意 Wiki 页面查看水印效果

### 高级功能
- **防复制保护**：启用后用户无法选择和复制页面文本
- **移动端适配**：水印会根据设备屏幕大小自动调整密度和透明度
- **动态占位符**：水印文本会实时显示当前用户和时间信息

## 🔧 故障排除

### 常见问题

**Q: 水印没有显示**
- 检查是否已启用水印功能
- 确认浏览器支持 Canvas（现代浏览器都支持）
- 检查透明度设置是否过低

**Q: 移动端水印显示异常**
- 检查 "移动端应用" 设置是否启用
- 尝试刷新页面或清除浏览器缓存

**Q: 配置保存失败**
- 确认当前用户具有管理员权限
- 检查 XWiki 日志中的错误信息
- 验证配置参数是否在有效范围内

**Q: 防复制功能影响正常使用**
- 防复制功能会禁用文本选择，可以在配置中关闭
- 表单输入框不受影响，仍可正常编辑

### 性能优化建议
- 适当调整水印间距，避免过于密集影响性能
- 在低性能设备上可以禁用移动端水印
- 透明度建议设置在 0.05-0.2 之间，既保证可见性又不影响阅读

## 🛠️ 开发和贡献

### 构建项目
```bash
# 克隆项目
git clone <repository-url>
cd xwiki-watermark

# 编译项目
mvn clean compile

# 打包扩展
mvn clean package

# 生成的 JAR 文件位于 target/xwiki-watermark-1.0-SNAPSHOT.jar
```

### 项目结构
```
xwiki-watermark/
├── pom.xml                                    # Maven 配置
├── README.md                                  # 项目文档
└── src/main/
    ├── java/com/microcredchina/xwikiwatermark/
    │   ├── WatermarkConfigurationService.java # 配置服务接口
    │   ├── WatermarkRenderingService.java     # 渲染服务接口
    │   └── internal/
    │       ├── DefaultWatermarkConfigurationService.java  # 配置服务实现
    │       ├── DefaultWatermarkRenderingService.java      # 渲染服务实现
    │       └── WatermarkAdministrationSection.java        # 管理面板集成
    └── resources/
        ├── META-INF/components.txt            # 组件注册
        ├── ApplicationResources.properties    # 英文国际化
        ├── ApplicationResources_zh.properties # 中文国际化
        └── watermark/
            ├── watermark.js                   # Canvas 渲染引擎
            └── watermark.css                  # 样式和移动端适配
```

### 技术架构
- **后端**：基于 XWiki 组件系统的 Java 服务
- **前端**：Canvas API + 响应式 CSS
- **配置**：XWiki.XWikiPreferences 存储
- **管理**：集成到 XWiki 标准管理面板

## 📄 许可证

本项目采用 GNU Lesser General Public License v2.1 许可证。详见 [LICENSE](LICENSE) 文件。

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📞 支持

如有问题或建议，请通过以下方式联系：
- 提交 [GitHub Issue](../../issues)
- 发送邮件至项目维护者

---

**版本**: 1.0-SNAPSHOT  
**最后更新**: 2025-08-11  
**兼容性**: XWiki 15.10.11+
