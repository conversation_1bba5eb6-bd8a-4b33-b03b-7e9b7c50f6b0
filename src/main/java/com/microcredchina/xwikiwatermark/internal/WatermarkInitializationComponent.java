/*
 * See the NOTICE file distributed with this work for additional
 * information regarding copyright ownership.
 *
 * This is free software; you can redistribute it and/or modify it
 * under the terms of the GNU Lesser General Public License as
 * published by the Free Software Foundation; either version 2.1 of
 * the License, or (at your option) any later version.
 *
 * This software is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this software; if not, write to the Free
 * Software Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA
 * 02110-1301 USA, or see the FSF site: http://www.fsf.org.
 */
package com.microcredchina.xwikiwatermark.internal;

import javax.inject.Inject;
import javax.inject.Named;
import javax.inject.Singleton;

import org.slf4j.Logger;
import org.xwiki.component.annotation.Component;
import org.xwiki.component.manager.ComponentLifecycleException;
import org.xwiki.component.phase.Initializable;
import org.xwiki.component.phase.InitializationException;
import org.xwiki.observation.EventListener;
import org.xwiki.observation.event.Event;
import org.xwiki.bridge.event.ApplicationReadyEvent;
import java.util.Arrays;
import java.util.List;
import org.xwiki.context.Execution;
import org.xwiki.context.ExecutionContext;
import org.xwiki.model.reference.DocumentReference;
import org.xwiki.model.reference.LocalDocumentReference;

import com.xpn.xwiki.objects.classes.BaseClass;

import com.xpn.xwiki.XWikiContext;
import com.xpn.xwiki.XWikiException;
import com.xpn.xwiki.doc.XWikiDocument;
import com.xpn.xwiki.objects.BaseObject;

/**
 * Initialization component for watermark extension.
 * 
 * This component automatically creates the necessary configuration pages and objects
 * when the extension is installed, enabling the administration panel integration
 * without manual setup.
 * 
 * @version $Id$
 * @since 1.0
 */
@Component
@Named("watermark.initialization")
@Singleton
public class WatermarkInitializationComponent implements EventListener
{
    /** Logger for this component. */
    @Inject
    private Logger logger;

    /** Execution context for accessing XWiki context. */
    @Inject
    private Execution execution;

    @Override
    public String getName()
    {
        return "watermark.initialization";
    }

    @Override
    public List<Event> getEvents()
    {
        return Arrays.asList(new ApplicationReadyEvent());
    }

    @Override
    public void onEvent(Event event, Object source, Object data)
    {
        try {
            logger.info("XWiki application ready, initializing Watermark Extension...");

            XWikiContext context = getXWikiContext();
            if (context != null) {
                createConfigurationClass(context);
                createConfigurationPage(context);
                logger.info("XWiki Watermark Extension initialization completed successfully");
            } else {
                logger.warn("XWiki context not available during initialization");
            }
        } catch (Exception e) {
            logger.error("Failed to initialize XWiki Watermark Extension", e);
            // Don't throw exception to avoid breaking XWiki startup
        }
    }

    /**
     * Create the watermark configuration class if it doesn't exist.
     * 
     * @param context XWiki context
     * @throws XWikiException if creation fails
     */
    private void createConfigurationClass(XWikiContext context) throws XWikiException
    {
        DocumentReference classRef = new DocumentReference("xwiki", "WatermarkCode", "WatermarkConfigurationClass");
        XWikiDocument classDoc = context.getWiki().getDocument(classRef, context);

        boolean changed = false;
        if (classDoc.isNew()) {
            logger.info("Creating WatermarkConfigurationClass...");
            classDoc.setTitle("Watermark Configuration Class");
            classDoc.setContent("{{include reference=\"XWiki.ClassSheet\"/}}");
            changed = true;
        }

        BaseClass xclass = classDoc.getXClass();

        // Boolean: enabled
        if (xclass.get("enabled") == null) {
            try { xclass.addBooleanField("enabled", "Enabled"); } catch (Throwable t) { /* ignore if not available */ }
            changed = true;
        }

        // String: textTemplate
        if (xclass.get("textTemplate") == null) {
            try { xclass.addTextField("textTemplate", "Text Template", 80); } catch (Throwable t) { /* ignore */ }
            changed = true;
        }

        // Numbers
        if (xclass.get("xSpacing") == null) { try { xclass.addNumberField("xSpacing", "Horizontal Spacing", 10, "integer"); } catch (Throwable t) { /* ignore */ } changed = true; }
        if (xclass.get("ySpacing") == null) { try { xclass.addNumberField("ySpacing", "Vertical Spacing", 10, "integer"); } catch (Throwable t) { /* ignore */ } changed = true; }
        if (xclass.get("angle") == null) { try { xclass.addNumberField("angle", "Rotation Angle", 10, "integer"); } catch (Throwable t) { /* ignore */ } changed = true; }
        if (xclass.get("fontSize") == null) { try { xclass.addNumberField("fontSize", "Font Size", 10, "integer"); } catch (Throwable t) { /* ignore */ } changed = true; }
        if (xclass.get("opacity") == null) { try { xclass.addNumberField("opacity", "Opacity", 10, "float"); } catch (Throwable t) { /* ignore */ } changed = true; }

        // Boolean: antiCopy, applyToMobile
        if (xclass.get("antiCopy") == null) { try { xclass.addBooleanField("antiCopy", "Anti-Copy Protection"); } catch (Throwable t) { /* ignore */ } changed = true; }
        if (xclass.get("applyToMobile") == null) { try { xclass.addBooleanField("applyToMobile", "Apply to Mobile"); } catch (Throwable t) { /* ignore */ } changed = true; }

        // Localize pretty names based on current language
        try {
            String lang = context.getLanguage();
            if (lang != null && lang.startsWith("zh")) {
                setPrettyNameIfPresent(xclass, "enabled", "启用水印");
                setPrettyNameIfPresent(xclass, "textTemplate", "水印文字模板");
                setPrettyNameIfPresent(xclass, "xSpacing", "水平间距");
                setPrettyNameIfPresent(xclass, "ySpacing", "垂直间距");
                setPrettyNameIfPresent(xclass, "angle", "旋转角度");
                setPrettyNameIfPresent(xclass, "opacity", "透明度");
                setPrettyNameIfPresent(xclass, "fontSize", "字体大小");
                setPrettyNameIfPresent(xclass, "antiCopy", "防复制保护");
                setPrettyNameIfPresent(xclass, "applyToMobile", "移动端显示");
            } else {
                setPrettyNameIfPresent(xclass, "enabled", "Enable Watermark");
                setPrettyNameIfPresent(xclass, "textTemplate", "Watermark Text Template");
                setPrettyNameIfPresent(xclass, "xSpacing", "Horizontal Spacing");
                setPrettyNameIfPresent(xclass, "ySpacing", "Vertical Spacing");
                setPrettyNameIfPresent(xclass, "angle", "Rotation Angle");
                setPrettyNameIfPresent(xclass, "opacity", "Opacity");
                setPrettyNameIfPresent(xclass, "fontSize", "Font Size");
                setPrettyNameIfPresent(xclass, "antiCopy", "Anti-Copy Protection");
                setPrettyNameIfPresent(xclass, "applyToMobile", "Apply to Mobile");
            }
            changed = true;
        } catch (Exception ignore) { }

        boolean authorChanged = false;
        // Only set authorship when creating a brand new document to avoid cache modification warnings
        if (classDoc.isNew()) {
            DocumentReference adminRef = new DocumentReference("xwiki", "XWiki", "Admin");
            authorChanged = ensureAuthorship(classDoc, adminRef);
        }

        if (changed || (classDoc.isNew() && authorChanged)) {
            context.getWiki().saveDocument(classDoc, classDoc.isNew() ? "Created watermark configuration class" : "Updated watermark configuration class", context);
            logger.info("WatermarkConfigurationClass {}", classDoc.isNew() ? "created" : "updated");
        }
    }

    /**
     * Add a property to the configuration class.
     * 
     * @param classDoc the class document
     * @param context XWiki context
     * @param name property name
     * @param type property type
     * @param prettyName property display name
     * @param tooltip property description
     * @throws XWikiException if adding property fails
     */
    private boolean addIntegerFieldIfMissing(BaseClass xclass, String name, String prettyName)
    {
        if (xclass.get(name) == null) {
            try {
                xclass.addNumberField(name, prettyName, 10, "integer");
            } catch (Throwable t) {
                return false;
            }
            return true;
        }
        return false;
    }

    /**
     * Create the watermark configuration page if it doesn't exist.
     *
     * @param context XWiki context
     * @throws XWikiException if creation fails
     */
    private void createConfigurationPage(XWikiContext context) throws XWikiException
    {
        DocumentReference configRef = new DocumentReference("xwiki", "WatermarkCode", "WatermarkConfiguration");
        XWikiDocument configDoc = context.getWiki().getDocument(configRef, context);

        boolean changed = false;
        if (configDoc.isNew()) {
            logger.info("Creating WatermarkConfiguration page...");
            configDoc.setTitle("Watermark Configuration");
            configDoc.setContent("This page contains the watermark configuration for the wiki.\n\n" +
                                "{{include reference=\"XWiki.ConfigurableClass\"/}}");
            changed = true;
        }

        // Add or update ConfigurableClass object
        LocalDocumentReference configurableClassRef = new LocalDocumentReference("XWiki", "ConfigurableClass");
        BaseObject configurableObj = configDoc.getXObject(configurableClassRef);
        if (configurableObj == null) {
            configurableObj = configDoc.newXObject(configurableClassRef, context);
            changed = true;
        }
        if (!"水印".equals(configurableObj.getStringValue("displayInSection"))) {
            configurableObj.setStringValue("displayInSection", "水印");
            changed = true;
        }
        // Use custom top-level category id 'watermark' and rely on admin.watermark i18n
        if (!"watermark".equals(configurableObj.getStringValue("displayInCategory"))) {
            configurableObj.setStringValue("displayInCategory", "watermark");
            changed = true;
        }
        if (!"WatermarkCode.WatermarkConfigurationClass".equals(configurableObj.getStringValue("configurationClass"))) {
            configurableObj.setStringValue("configurationClass", "WatermarkCode.WatermarkConfigurationClass");
            changed = true;
        }
        if (!"WIKI".equals(configurableObj.getStringValue("scope"))) {
            configurableObj.setStringValue("scope", "WIKI");
            changed = true;
        }
        if (configurableObj.getIntValue("sectionOrder") == 0) {
            configurableObj.setIntValue("sectionOrder", 1000);
            changed = true;
        }

        // Ensure configuration class object exists with defaults
        LocalDocumentReference watermarkClassRef = new LocalDocumentReference("WatermarkCode", "WatermarkConfigurationClass");
        BaseObject watermarkObj = configDoc.getXObject(watermarkClassRef);
        if (watermarkObj == null) {
            watermarkObj = configDoc.newXObject(watermarkClassRef, context);
            watermarkObj.setIntValue("enabled", 0);
            watermarkObj.setStringValue("textTemplate", "${user} - ${timestamp}");
            watermarkObj.setIntValue("xSpacing", 200);
            watermarkObj.setIntValue("ySpacing", 100);
            watermarkObj.setIntValue("angle", -30);
            watermarkObj.setFloatValue("opacity", 0.1f);
            watermarkObj.setIntValue("fontSize", 14);
            watermarkObj.setIntValue("antiCopy", 0);
            watermarkObj.setIntValue("applyToMobile", 1);
            changed = true;
        }

        boolean authorChanged = false;
        if (configDoc.isNew()) {
            DocumentReference adminRef = new DocumentReference("xwiki", "XWiki", "Admin");
            authorChanged = ensureAuthorship(configDoc, adminRef);
        }

        // Also ensure Skin Extensions (SSX/JSX) exist on this page
        ensureSkinExtensions(context, configDoc);

        if (changed || (configDoc.isNew() && authorChanged)) {
            context.getWiki().saveDocument(configDoc, configDoc.isNew() ? "Created watermark configuration page" : "Updated watermark configuration page", context);
            logger.info("WatermarkConfiguration page {}", configDoc.isNew() ? "created" : "updated");
        }
    }

    /**
     * Ensure StyleSheetExtension (SSX) and JavaScriptExtension (JSX) objects exist with proper code.
     * This uses SkinExtension mechanism to load resources across skins.
     */
    private void ensureSkinExtensions(XWikiContext context, XWikiDocument configDoc) throws XWikiException
    {
        // SSX
        LocalDocumentReference ssxRef = new LocalDocumentReference("XWiki", "StyleSheetExtension");
        BaseObject ssx = configDoc.getXObject(ssxRef);
        if (ssx == null) {
            ssx = configDoc.newXObject(ssxRef, context);
            ssx.setStringValue("name", "WatermarkCSS");
        }
        String css = "#xwiki-watermark-canvas{position:fixed;top:0;left:0;width:100%;height:100%;pointer-events:none;z-index:100;opacity:0.1;}" +
                     ".xwiki-watermark-protected{-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;}";
        ssx.setLargeStringValue("code", css);
        // Try to set usage flags (best-effort)
        try { ssx.setStringValue("use", "always"); } catch (Throwable ignore) { }
        try { ssx.setIntValue("parse", 0); } catch (Throwable ignore) { }

        // JSX
        LocalDocumentReference jsxRef = new LocalDocumentReference("XWiki", "JavaScriptExtension");
        BaseObject jsx = configDoc.getXObject(jsxRef);
        if (jsx == null) {
            jsx = configDoc.newXObject(jsxRef, context);
            jsx.setStringValue("name", "WatermarkJS");
        }
        String js = "document.addEventListener('DOMContentLoaded',function(){" +
                    "try {" +
                    "var base=((window.XWiki&&XWiki.contextPath)?XWiki.contextPath:'')+'/rest/wikis/xwiki/watermark';" +
                    "fetch(base+'/status',{credentials:'same-origin'}).then(r=>r.json()).then(d=>{" +
                    "if(d&&d.shouldApply){" +
                    "fetch(base+'/config',{credentials:'same-origin'}).then(r=>r.json()).then(c=>{" +
                    "var canvas=document.getElementById('xwiki-watermark-canvas');" +
                    "if(!canvas){canvas=document.createElement('canvas');canvas.id='xwiki-watermark-canvas';document.body.appendChild(canvas);}" +
                    "var ctx=canvas.getContext('2d');canvas.width=window.innerWidth;canvas.height=window.innerHeight;" +
                    "ctx.clearRect(0,0,canvas.width,canvas.height);ctx.save();" +
                    "ctx.font=c.fontSize+'px Arial';ctx.fillStyle='rgba(0,0,0,'+c.opacity+')';ctx.rotate(c.angle*Math.PI/180);" +
                    "fetch(base+'/text',{credentials:'same-origin'}).then(r=>r.json()).then(t=>{" +
                    "for(var y=0;y<canvas.height+200;y+=c.ySpacing){for(var x=0;x<canvas.width+200;x+=c.xSpacing){ctx.fillText(t.text,x,y);}}" +
                    "ctx.restore();if(c.antiCopy)document.body.classList.add('xwiki-watermark-protected');" +
                    "})})}}).catch(e=>console.warn('Watermark failed:',e));" +
                    "} catch(e){console.warn('Watermark error',e);}" +
                    "});";
        jsx.setLargeStringValue("code", js);
        try { jsx.setStringValue("use", "always"); } catch (Throwable ignore) { }
        try { jsx.setIntValue("parse", 0); } catch (Throwable ignore) { }
    }

    /**
     * Ensure creator/author/contentAuthor are set to a user with script right (e.g., Admin) to allow Velocity.
     *
     * @return true if any field changed
     */
    private boolean ensureAuthorship(XWikiDocument doc, DocumentReference authorRef)
    {
        boolean changed = false;
        if (doc.getCreatorReference() == null || !doc.getCreatorReference().equals(authorRef)) {
            doc.setCreatorReference(authorRef);
            changed = true;
        }
        if (doc.getAuthorReference() == null || !doc.getAuthorReference().equals(authorRef)) {
            doc.setAuthorReference(authorRef);
            changed = true;
        }
        if (doc.getContentAuthorReference() == null || !doc.getContentAuthorReference().equals(authorRef)) {
            doc.setContentAuthorReference(authorRef);
            changed = true;
        }
        return changed;
    }

    private void setPrettyNameIfPresent(BaseClass xclass, String fieldName, String pretty)
    {
        try {
            if (xclass.get(fieldName) != null && xclass.get(fieldName) instanceof com.xpn.xwiki.objects.classes.PropertyClass) {
                ((com.xpn.xwiki.objects.classes.PropertyClass) xclass.get(fieldName)).setPrettyName(pretty);
            }
        } catch (Exception ignore) { }
    }

    /**
     * Get XWiki context from execution context.
     * 
     * @return XWiki context or null if not available
     */
    private XWikiContext getXWikiContext()
    {
        try {
            ExecutionContext executionContext = execution.getContext();
            if (executionContext != null) {
                return (XWikiContext) executionContext.getProperty(XWikiContext.EXECUTIONCONTEXT_KEY);
            }
        } catch (Exception e) {
            logger.debug("XWiki context not available", e);
        }
        return null;
    }
}
