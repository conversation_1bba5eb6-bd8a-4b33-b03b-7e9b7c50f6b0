/*
 * See the NOTICE file distributed with this work for additional
 * information regarding copyright ownership.
 *
 * This is free software; you can redistribute it and/or modify it
 * under the terms of the GNU Lesser General Public License as
 * published by the Free Software Foundation; either version 2.1 of
 * the License, or (at your option) any later version.
 *
 * This software is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this software; if not, write to the Free
 * Software Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA
 * 02110-1301 USA, or see the FSF site: http://www.fsf.org.
 */
package com.microcredchina.xwikiwatermark.internal;

import javax.inject.Inject;
import javax.inject.Named;
import javax.inject.Singleton;

import org.xwiki.component.annotation.Component;
import org.xwiki.configuration.ConfigurationSource;

import com.microcredchina.xwikiwatermark.WatermarkConfigurationService;

/**
 * Configuration handler for watermark settings using XWiki ConfigurableClass mechanism.
 * 
 * This class provides the bridge between XWiki's administration interface and
 * the watermark configuration service, handling form processing and validation.
 * 
 * @version $Id$
 * @since 1.0
 */
@Component
@Named("watermark")
@Singleton
public class WatermarkConfigurationClass
{
    /** Configuration service for accessing watermark settings. */
    @Inject
    private WatermarkConfigurationService configurationService;

    /** Configuration source for accessing XWiki preferences. */
    @Inject
    private ConfigurationSource configurationSource;

    /**
     * Save watermark configuration from administration form.
     * This method is called when the administration form is submitted.
     * 
     * @param enabled whether watermark is enabled
     * @param textTemplate watermark text template
     * @param xSpacing horizontal spacing between watermarks
     * @param ySpacing vertical spacing between watermarks
     * @param angle rotation angle of watermarks
     * @param opacity transparency level of watermarks
     * @param fontSize font size of watermark text
     * @param antiCopy whether anti-copy protection is enabled
     * @param applyToMobile whether to apply watermark on mobile devices
     * @throws Exception if saving fails
     */
    public void saveConfiguration(
        Boolean enabled,
        String textTemplate,
        Integer xSpacing,
        Integer ySpacing,
        Integer angle,
        Float opacity,
        Integer fontSize,
        Boolean antiCopy,
        Boolean applyToMobile
    ) throws Exception
    {
        if (enabled != null) {
            configurationService.setConfig("enabled", enabled);
        }
        if (textTemplate != null && !textTemplate.trim().isEmpty()) {
            configurationService.setConfig("textTemplate", textTemplate.trim());
        }
        if (xSpacing != null && xSpacing >= 50 && xSpacing <= 500) {
            configurationService.setConfig("xSpacing", xSpacing);
        }
        if (ySpacing != null && ySpacing >= 30 && ySpacing <= 300) {
            configurationService.setConfig("ySpacing", ySpacing);
        }
        if (angle != null && angle >= -180 && angle <= 180) {
            configurationService.setConfig("angle", angle);
        }
        if (opacity != null && opacity >= 0.0f && opacity <= 1.0f) {
            configurationService.setConfig("opacity", opacity);
        }
        if (fontSize != null && fontSize >= 8 && fontSize <= 48) {
            configurationService.setConfig("fontSize", fontSize);
        }
        if (antiCopy != null) {
            configurationService.setConfig("antiCopy", antiCopy);
        }
        if (applyToMobile != null) {
            configurationService.setConfig("applyToMobile", applyToMobile);
        }

        // Refresh cache after saving
        configurationService.refreshCache();
    }

    /**
     * Get current configuration values for form display.
     * 
     * @return configuration values map
     */
    public java.util.Map<String, Object> getCurrentConfiguration()
    {
        return configurationService.getAllConfigs();
    }

    /**
     * Get enabled configuration value.
     * 
     * @return true if watermark is enabled
     */
    public boolean getEnabled()
    {
        return configurationService.getBooleanConfig("enabled", false);
    }

    /**
     * Get text template configuration value.
     * 
     * @return watermark text template
     */
    public String getTextTemplate()
    {
        return configurationService.getStringConfig("textTemplate", "${user} - ${timestamp}");
    }

    /**
     * Get horizontal spacing configuration value.
     * 
     * @return horizontal spacing in pixels
     */
    public int getXSpacing()
    {
        return configurationService.getIntConfig("xSpacing", 200);
    }

    /**
     * Get vertical spacing configuration value.
     * 
     * @return vertical spacing in pixels
     */
    public int getYSpacing()
    {
        return configurationService.getIntConfig("ySpacing", 100);
    }

    /**
     * Get rotation angle configuration value.
     * 
     * @return rotation angle in degrees
     */
    public int getAngle()
    {
        return configurationService.getIntConfig("angle", -30);
    }

    /**
     * Get opacity configuration value.
     * 
     * @return opacity level (0.0 to 1.0)
     */
    public float getOpacity()
    {
        return configurationService.getFloatConfig("opacity", 0.1f);
    }

    /**
     * Get font size configuration value.
     * 
     * @return font size in pixels
     */
    public int getFontSize()
    {
        return configurationService.getIntConfig("fontSize", 14);
    }

    /**
     * Get anti-copy protection configuration value.
     * 
     * @return true if anti-copy protection is enabled
     */
    public boolean getAntiCopy()
    {
        return configurationService.getBooleanConfig("antiCopy", false);
    }

    /**
     * Get apply to mobile configuration value.
     * 
     * @return true if watermark should be applied on mobile devices
     */
    public boolean getApplyToMobile()
    {
        return configurationService.getBooleanConfig("applyToMobile", true);
    }
}
