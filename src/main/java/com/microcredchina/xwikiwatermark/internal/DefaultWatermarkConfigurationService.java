/*
 * See the NOTICE file distributed with this work for additional
 * information regarding copyright ownership.
 *
 * This is free software; you can redistribute it and/or modify it
 * under the terms of the GNU Lesser General Public License as
 * published by the Free Software Foundation; either version 2.1 of
 * the License, or (at your option) any later version.
 *
 * This software is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this software; if not, write to the Free
 * Software Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA
 * 02110-1301 USA, or see the FSF site: http://www.fsf.org.
 */
package com.microcredchina.xwikiwatermark.internal;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.locks.ReadWriteLock;
import java.util.concurrent.locks.ReentrantReadWriteLock;

import javax.inject.Inject;
import javax.inject.Provider;
import javax.inject.Singleton;

import org.xwiki.component.annotation.Component;
import org.xwiki.configuration.ConfigurationSource;
import org.xwiki.context.Execution;
import org.xwiki.context.ExecutionContext;

import com.xpn.xwiki.XWikiContext;
import com.xpn.xwiki.XWikiException;
import com.xpn.xwiki.doc.XWikiDocument;
import com.xpn.xwiki.objects.BaseObject;
import org.xwiki.model.reference.DocumentReference;
import org.xwiki.model.reference.LocalDocumentReference;

import com.microcredchina.xwikiwatermark.WatermarkConfigurationService;

/**
 * Default implementation of {@link WatermarkConfigurationService}.
 * 
 * This implementation provides thread-safe access to watermark configuration
 * stored in XWiki.XWikiPreferences using a read-write lock protected cache.
 * 
 * @version $Id$
 * @since 1.0
 */
@Component
@Singleton
public class DefaultWatermarkConfigurationService implements WatermarkConfigurationService
{
    /** Configuration key prefix. */
    private static final String CONFIG_PREFIX = "watermark.";

    /** Configuration source for accessing XWiki preferences. */
    @Inject
    private ConfigurationSource configurationSource;

    /** Execution context for accessing XWiki context. */
    @Inject
    private Execution execution;

    /** Read-write lock for protecting the configuration cache. */
    private final ReadWriteLock lock = new ReentrantReadWriteLock();

    /** Configuration cache. */
    private volatile Map<String, Object> configCache;

    /** Default configuration values. */
    private static final Map<String, Object> DEFAULT_VALUES = new HashMap<>();

    static {
        DEFAULT_VALUES.put("enabled", false);
        DEFAULT_VALUES.put("textTemplate", "${user} - ${timestamp}");
        DEFAULT_VALUES.put("xSpacing", 200);
        DEFAULT_VALUES.put("ySpacing", 100);
        DEFAULT_VALUES.put("angle", -30);
        DEFAULT_VALUES.put("opacity", 0.1f);
        DEFAULT_VALUES.put("fontSize", 14);
        DEFAULT_VALUES.put("antiCopy", false);
        DEFAULT_VALUES.put("applyToMobile", true);
    }

    @Override
    public Object getConfig(String key)
    {
        lock.readLock().lock();
        try {
            if (configCache == null) {
                // Initialize cache if not already done
                lock.readLock().unlock();
                lock.writeLock().lock();
                try {
                    if (configCache == null) {
                        loadConfigCache();
                    }
                    lock.readLock().lock();
                } finally {
                    lock.writeLock().unlock();
                }
            }
            
            Object value = configCache.get(key);
            return value != null ? value : DEFAULT_VALUES.get(key);
        } finally {
            lock.readLock().unlock();
        }
    }

    @Override
    public void setConfig(String key, Object value) throws Exception
    {
        lock.writeLock().lock();
        try {
            // Save to XWiki preferences
            String fullKey = CONFIG_PREFIX + key;

            // Get XWiki context
            XWikiContext context = getXWikiContext();
            if (context != null) {
                // Get or create XWikiPreferences document
                DocumentReference prefsRef = new DocumentReference("xwiki", "XWiki", "XWikiPreferences");
                XWikiDocument prefsDoc = context.getWiki().getDocument(prefsRef, context);
                LocalDocumentReference prefsClassRef = new LocalDocumentReference("XWiki", "XWikiPreferences");
                BaseObject prefsObj = prefsDoc.getXObject(prefsClassRef);

                if (prefsObj == null) {
                    // Create preferences object if it doesn't exist
                    prefsObj = prefsDoc.newXObject(prefsClassRef, context);
                }

                // Set the configuration value
                if (value instanceof String) {
                    prefsObj.setStringValue(fullKey, (String) value);
                } else if (value instanceof Integer) {
                    prefsObj.setIntValue(fullKey, (Integer) value);
                } else if (value instanceof Float || value instanceof Double) {
                    prefsObj.setFloatValue(fullKey, ((Number) value).floatValue());
                } else if (value instanceof Boolean) {
                    prefsObj.setIntValue(fullKey, ((Boolean) value) ? 1 : 0);
                } else {
                    prefsObj.setStringValue(fullKey, value != null ? value.toString() : "");
                }

                // Save the document
                context.getWiki().saveDocument(prefsDoc, "Updated watermark configuration", context);
            }

            // Update cache
            if (configCache == null) {
                loadConfigCache();
            }
            configCache.put(key, value);
        } finally {
            lock.writeLock().unlock();
        }
    }

    @Override
    public Map<String, Object> getAllConfigs()
    {
        Map<String, Object> result = new HashMap<>();
        
        // Add all default values first
        result.putAll(DEFAULT_VALUES);
        
        // Override with actual configured values
        lock.readLock().lock();
        try {
            if (configCache == null) {
                lock.readLock().unlock();
                lock.writeLock().lock();
                try {
                    if (configCache == null) {
                        loadConfigCache();
                    }
                    lock.readLock().lock();
                } finally {
                    lock.writeLock().unlock();
                }
            }
            
            result.putAll(configCache);
        } finally {
            lock.readLock().unlock();
        }
        
        return result;
    }

    @Override
    public boolean getBooleanConfig(String key, boolean defaultValue)
    {
        Object value = getConfig(key);
        if (value instanceof Boolean) {
            return (Boolean) value;
        }
        if (value instanceof String) {
            return Boolean.parseBoolean((String) value);
        }
        return defaultValue;
    }

    @Override
    public int getIntConfig(String key, int defaultValue)
    {
        Object value = getConfig(key);
        if (value instanceof Integer) {
            return (Integer) value;
        }
        if (value instanceof String) {
            try {
                return Integer.parseInt((String) value);
            } catch (NumberFormatException e) {
                return defaultValue;
            }
        }
        return defaultValue;
    }

    @Override
    public float getFloatConfig(String key, float defaultValue)
    {
        Object value = getConfig(key);
        if (value instanceof Float) {
            return (Float) value;
        }
        if (value instanceof Double) {
            return ((Double) value).floatValue();
        }
        if (value instanceof String) {
            try {
                return Float.parseFloat((String) value);
            } catch (NumberFormatException e) {
                return defaultValue;
            }
        }
        return defaultValue;
    }

    @Override
    public String getStringConfig(String key, String defaultValue)
    {
        Object value = getConfig(key);
        return value != null ? value.toString() : defaultValue;
    }

    @Override
    public boolean isEnabled()
    {
        return getBooleanConfig("enabled", false);
    }

    @Override
    public void refreshCache()
    {
        lock.writeLock().lock();
        try {
            configCache = null;
        } finally {
            lock.writeLock().unlock();
        }
    }

    /**
     * Load configuration from XWiki preferences into cache.
     */
    private void loadConfigCache()
    {
        configCache = new HashMap<>();

        // 1) Try to load from Watermark configuration document first
        try {
            XWikiContext context = getXWikiContext();
            if (context != null) {
                DocumentReference cfgRef = new DocumentReference("xwiki", "WatermarkCode", "WatermarkConfiguration");
                XWikiDocument cfgDoc = context.getWiki().getDocument(cfgRef, context);
                if (cfgDoc != null) {
                    LocalDocumentReference wmClassRef = new LocalDocumentReference("WatermarkCode", "WatermarkConfigurationClass");
                    BaseObject wmObj = cfgDoc.getXObject(wmClassRef);
                    if (wmObj != null) {
                        for (String key : DEFAULT_VALUES.keySet()) {
                            Object value = readField(wmObj, key, DEFAULT_VALUES.get(key));
                            if (value != null) {
                                configCache.put(key, value);
                            }
                        }
                    }
                }
            }
        } catch (Exception ignore) {
            // ignore and try fallbacks
        }

        // 2) Fallback to XWikiPreferences (watermark.* custom keys)
        try {
            XWikiContext context = getXWikiContext();
            if (context != null) {
                DocumentReference prefsRef = new DocumentReference("xwiki", "XWiki", "XWikiPreferences");
                XWikiDocument prefsDoc = context.getWiki().getDocument(prefsRef, context);
                LocalDocumentReference prefsClassRef = new LocalDocumentReference("XWiki", "XWikiPreferences");
                BaseObject prefsObj = prefsDoc.getXObject(prefsClassRef);
                if (prefsObj != null) {
                    for (String key : DEFAULT_VALUES.keySet()) {
                        if (configCache.containsKey(key)) {
                            continue;
                        }
                        String fullKey = CONFIG_PREFIX + key;
                        Object value = prefsObj.get(fullKey);
                        if (value != null) {
                            configCache.put(key, value);
                        }
                    }
                }
            }
        } catch (Exception ignore) {
            // ignore
        }

        // 3) Fallback to configuration source (xwiki.properties)
        for (String key : DEFAULT_VALUES.keySet()) {
            if (configCache.containsKey(key)) {
                continue;
            }
            String fullKey = CONFIG_PREFIX + key;
            Object value = configurationSource.getProperty(fullKey);
            if (value != null) {
                configCache.put(key, value);
            }
        }
    }

    private Object readField(BaseObject obj, String key, Object defaultValue)
    {
        try {
            if (defaultValue instanceof Boolean) {
                return obj.getIntValue(key) == 1;
            } else if (defaultValue instanceof Integer) {
                return obj.getIntValue(key);
            } else if (defaultValue instanceof Float) {
                try {
                    return obj.getFloatValue(key);
                } catch (Throwable t) {
                    String s = obj.getStringValue(key);
                    return (s != null && !s.isEmpty()) ? Float.parseFloat(s) : null;
                }
            } else if (defaultValue instanceof String) {
                String s = obj.getStringValue(key);
                return (s != null && !s.isEmpty()) ? s : null;
            } else {
                Object raw = obj.get(key);
                return raw;
            }
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * Get XWiki context from execution context.
     *
     * @return XWiki context or null if not available
     */
    private XWikiContext getXWikiContext()
    {
        try {
            ExecutionContext executionContext = execution.getContext();
            if (executionContext != null) {
                return (XWikiContext) executionContext.getProperty(XWikiContext.EXECUTIONCONTEXT_KEY);
            }
        } catch (Exception e) {
            // Context not available
        }
        return null;
    }
}
