/*
 * See the NOTICE file distributed with this work for additional
 * information regarding copyright ownership.
 *
 * This is free software; you can redistribute it and/or modify it
 * under the terms of the GNU Lesser General Public License as
 * published by the Free Software Foundation; either version 2.1 of
 * the License, or (at your option) any later version.
 *
 * This software is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this software; if not, write to the Free
 * Software Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA
 * 02110-1301 USA, or see the FSF site: http://www.fsf.org.
 */
package com.microcredchina.xwikiwatermark.internal;

import javax.inject.Inject;
import javax.inject.Named;
import javax.inject.Singleton;

import org.xwiki.component.annotation.Component;
import org.xwiki.uiextension.UIExtension;
import org.xwiki.rendering.block.Block;
import org.xwiki.rendering.block.RawBlock;
import org.xwiki.rendering.syntax.Syntax;

import com.microcredchina.xwikiwatermark.WatermarkRenderingService;

/**
 * UI Extension for automatically loading watermark resources on all pages.
 * 
 * This component injects the necessary JavaScript and CSS files into the HTML head
 * of every XWiki page, enabling the watermark functionality without manual setup.
 * 
 * @version $Id$
 * @since 1.0
 */
@Component
@Named("watermark.ui")
@Singleton
public class WatermarkUIExtension implements UIExtension
{
    /** Rendering service for checking if watermark should be applied. */
    @Inject
    private WatermarkRenderingService renderingService;

    @Override
    public String getId()
    {
        return "watermark.ui";
    }

    @Override
    public String getExtensionPointId()
    {
        return "org.xwiki.platform.template.header";
    }

    @Override
    public java.util.Map<String, String> getParameters()
    {
        return java.util.Collections.emptyMap();
    }

    @Override
    public Block execute()
    {
        try {
            // Return inline watermark initialization code as HTML block; client will decide via /status
            String html = "<style type=\"text/css\">" +
                         "#xwiki-watermark-canvas{position:fixed;top:0;left:0;width:100%;height:100%;pointer-events:none;z-index:100;opacity:0.1;}" +
                         ".xwiki-watermark-protected{-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;}" +
                         "</style>" +
                         "<script type=\"text/javascript\">" +
                         "document.addEventListener('DOMContentLoaded',function(){" +
                          "var wiki=(window.XWiki&&XWiki.currentWiki)?XWiki.currentWiki:'xwiki';" +
                          "var base=((window.XWiki&&XWiki.contextPath)?XWiki.contextPath:'')+'/rest/wikis/'+wiki+'/watermark';" +
                         "fetch(base+'/status',{credentials:'same-origin'}).then(r=>r.json()).then(d=>{" +
                         "if(d && d.shouldApply){" +
                         "fetch(base+'/config',{credentials:'same-origin'}).then(r=>r.json()).then(c=>{" +
                         "var canvas=document.createElement('canvas');" +
                         "canvas.id='xwiki-watermark-canvas';" +
                         "document.body.appendChild(canvas);" +
                         "var ctx=canvas.getContext('2d');" +
                         "canvas.width=window.innerWidth;canvas.height=window.innerHeight;" +
                         "ctx.font=c.fontSize+'px Arial';ctx.fillStyle='rgba(0,0,0,'+c.opacity+')';" +
                         "ctx.rotate(c.angle*Math.PI/180);" +
                         "fetch(base+'/text',{credentials:'same-origin'}).then(r=>r.json()).then(t=>{" +
                         "for(var y=0;y<canvas.height+200;y+=c.ySpacing){" +
                         "for(var x=0;x<canvas.width+200;x+=c.xSpacing){" +
                         "ctx.fillText(t.text,x,y);}}" +
                         "if(c.antiCopy)document.body.classList.add('xwiki-watermark-protected');" +
                         "})})}" +
                         "}).catch(e=>console.warn('Watermark failed:',e));" +
                         "});" +
                         "</script>";

            return new RawBlock(html, Syntax.HTML_5_0);
        } catch (Exception e) {
            // Return empty block if any error occurs to avoid breaking page rendering
            return new RawBlock("", Syntax.HTML_5_0);
        }
    }
}
