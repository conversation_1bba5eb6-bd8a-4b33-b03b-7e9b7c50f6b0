/*
 * See the NOTICE file distributed with this work for additional
 * information regarding copyright ownership.
 *
 * This is free software; you can redistribute it and/or modify it
 * under the terms of the GNU Lesser General Public License as
 * published by the Free Software Foundation; either version 2.1 of
 * the License, or (at your option) any later version.
 *
 * This software is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this software; if not, write to the Free
 * Software Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA
 * 02110-1301 USA, or see the FSF site: http://www.fsf.org.
 */
package com.microcredchina.xwikiwatermark.internal;

import javax.inject.Named;
import javax.inject.Singleton;
import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

import org.xwiki.component.annotation.Component;
import org.xwiki.rest.XWikiResource;
import com.xpn.xwiki.web.Utils;

import com.microcredchina.xwikiwatermark.WatermarkConfigurationService;
import com.microcredchina.xwikiwatermark.WatermarkRenderingService;

/**
 * REST resource for watermark configuration and user information.
 *
 * Provides AJAX endpoints for frontend to retrieve watermark configuration
 * and current user information.
 *
 * @version $Id$
 * @since 1.0
 */
@Component
@Named("watermark")
@Singleton
@Path("/wikis/{wikiName}/watermark")
public class WatermarkRestResource extends XWikiResource
{
    private WatermarkConfigurationService getConfigurationService()
    {
        return Utils.getComponent(WatermarkConfigurationService.class);
    }

    private WatermarkRenderingService getRenderingService()
    {
        return Utils.getComponent(WatermarkRenderingService.class);
    }

    /**
     * Get watermark configuration as JSON.
     * 
     * @return Response containing watermark configuration JSON
     */
    @GET
    @Path("/config")
    @Produces(MediaType.APPLICATION_JSON)
    public Response getWatermarkConfig()
    {
        try {
            // Check if watermark is enabled
            if (!getConfigurationService().isEnabled()) {
                return Response.ok("{\"enabled\": false}").build();
            }

            // Get configuration JSON
            String configJson = getRenderingService().getWatermarkConfigJson();
            return Response.ok(configJson).build();
        } catch (Exception e) {
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                          .entity("{\"error\": \"Failed to load watermark configuration\"}")
                          .build();
        }
    }

    /**
     * Get current user information.
     * 
     * @return Response containing user information JSON
     */
    @GET
    @Path("/user")
    @Produces(MediaType.APPLICATION_JSON)
    public Response getCurrentUserInfo()
    {
        try {
            String currentUser = getRenderingService().getCurrentUser();
            String timestamp = getRenderingService().getFormattedTimestamp();
            
            String userJson = String.format(
                "{\"user\": \"%s\", \"timestamp\": \"%s\"}",
                escapeJsonString(currentUser),
                escapeJsonString(timestamp)
            );
            
            return Response.ok(userJson).build();
        } catch (Exception e) {
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                          .entity("{\"error\": \"Failed to get user information\"}")
                          .build();
        }
    }

    /**
     * Check if watermark should be applied for current request.
     * 
     * @return Response containing application status JSON
     */
    @GET
    @Path("/status")
    @Produces(MediaType.APPLICATION_JSON)
    public Response getWatermarkStatus()
    {
        try {
            boolean shouldApply = getRenderingService().shouldApplyWatermark();
            String statusJson = String.format("{\"shouldApply\": %s}", shouldApply);
            
            return Response.ok(statusJson).build();
        } catch (Exception e) {
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                          .entity("{\"error\": \"Failed to check watermark status\"}")
                          .build();
        }
    }

    /**
     * Get resolved watermark text with placeholders replaced.
     * 
     * @return Response containing resolved text JSON
     */
    @GET
    @Path("/text")
    @Produces(MediaType.APPLICATION_JSON)
    public Response getResolvedText()
    {
        try {
            String resolvedText = getRenderingService().getResolvedWatermarkText();
            String textJson = String.format(
                "{\"text\": \"%s\"}",
                escapeJsonString(resolvedText)
            );
            
            return Response.ok(textJson).build();
        } catch (Exception e) {
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                          .entity("{\"error\": \"Failed to resolve watermark text\"}")
                          .build();
        }
    }

    /**
     * Escape special characters for JSON string.
     * 
     * @param str the string to escape
     * @return escaped string
     */
    private String escapeJsonString(String str)
    {
        if (str == null) {
            return "";
        }
        
        return str.replace("\\", "\\\\")
                  .replace("\"", "\\\"")
                  .replace("\n", "\\n")
                  .replace("\r", "\\r")
                  .replace("\t", "\\t");
    }
}
