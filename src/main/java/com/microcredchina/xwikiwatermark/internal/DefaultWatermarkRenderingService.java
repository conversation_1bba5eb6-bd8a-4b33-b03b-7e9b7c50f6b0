/*
 * See the NOTICE file distributed with this work for additional
 * information regarding copyright ownership.
 *
 * This is free software; you can redistribute it and/or modify it
 * under the terms of the GNU Lesser General Public License as
 * published by the Free Software Foundation; either version 2.1 of
 * the License, or (at your option) any later version.
 *
 * This software is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this software; if not, write to the Free
 * Software Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA
 * 02110-1301 USA, or see the FSF site: http://www.fsf.org.
 */
package com.microcredchina.xwikiwatermark.internal;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.inject.Inject;
import javax.inject.Singleton;

import org.xwiki.component.annotation.Component;
import org.xwiki.context.Execution;
import org.xwiki.context.ExecutionContext;

import com.xpn.xwiki.XWikiContext;
import com.xpn.xwiki.user.api.XWikiUser;

import com.microcredchina.xwikiwatermark.WatermarkConfigurationService;
import com.microcredchina.xwikiwatermark.WatermarkRenderingService;

/**
 * Default implementation of {@link WatermarkRenderingService}.
 * 
 * This implementation handles placeholder resolution and provides watermark
 * rendering parameters for frontend consumption.
 * 
 * @version $Id$
 * @since 1.0
 */
@Component
@Singleton
public class DefaultWatermarkRenderingService implements WatermarkRenderingService
{
    /** Pattern for matching placeholders. */
    private static final Pattern PLACEHOLDER_PATTERN = Pattern.compile("\\$\\{([^}]+)\\}");

    /** Default timestamp format. */
    private static final String DEFAULT_TIMESTAMP_FORMAT = "yyyy-MM-dd HH:mm:ss";

    /** Configuration service for accessing watermark settings. */
    @Inject
    private WatermarkConfigurationService configurationService;

    /** Execution context for accessing XWiki context. */
    @Inject
    private Execution execution;

    @Override
    public String resolveTemplate(String template)
    {
        if (template == null || template.isEmpty()) {
            return template;
        }

        String result = template;
        Matcher matcher = PLACEHOLDER_PATTERN.matcher(template);
        
        while (matcher.find()) {
            String placeholder = matcher.group(0); // Full placeholder like ${user}
            String key = matcher.group(1); // Just the key like "user"
            
            String replacement = resolvePlaceholder(key);
            result = result.replace(placeholder, replacement);
        }
        
        return result;
    }

    @Override
    public Map<String, Object> getWatermarkConfig()
    {
        Map<String, Object> config = new HashMap<>();
        
        // Get all configuration parameters
        Map<String, Object> allConfigs = configurationService.getAllConfigs();
        config.putAll(allConfigs);
        
        // Add resolved watermark text
        String textTemplate = configurationService.getStringConfig("textTemplate", "${user} - ${timestamp}");
        config.put("resolvedText", resolveTemplate(textTemplate));
        
        return config;
    }

    @Override
    public String getWatermarkConfigJson()
    {
        Map<String, Object> config = getWatermarkConfig();
        
        // Simple JSON serialization (in production, consider using a proper JSON library)
        StringBuilder json = new StringBuilder();
        json.append("{");
        
        boolean first = true;
        for (Map.Entry<String, Object> entry : config.entrySet()) {
            if (!first) {
                json.append(",");
            }
            first = false;
            
            json.append("\"").append(entry.getKey()).append("\":");
            Object value = entry.getValue();
            
            if (value instanceof String) {
                json.append("\"").append(escapeJsonString((String) value)).append("\"");
            } else if (value instanceof Boolean) {
                json.append(value.toString());
            } else if (value instanceof Number) {
                json.append(value.toString());
            } else {
                json.append("\"").append(value != null ? escapeJsonString(value.toString()) : "").append("\"");
            }
        }
        
        json.append("}");
        return json.toString();
    }

    @Override
    public String getResolvedWatermarkText()
    {
        String textTemplate = configurationService.getStringConfig("textTemplate", "${user} - ${timestamp}");
        return resolveTemplate(textTemplate);
    }

    @Override
    public boolean shouldApplyWatermark()
    {
        // Check if watermark is enabled
            if (!configurationService.isEnabled()) {
            return false;
        }

        // Check mobile setting
        boolean applyToMobile = configurationService.getBooleanConfig("applyToMobile", true);
        if (!applyToMobile && isMobileRequest()) {
            return false;
        }

        return true;
    }

    @Override
    public String getCurrentUser()
    {
        try {
            XWikiContext context = getXWikiContext();
            if (context != null) {
                org.xwiki.model.reference.DocumentReference userRef = context.getUserReference();
                if (userRef != null) {
                    String userName = userRef.getName();
                    if (userName != null && !userName.isEmpty() && !"XWikiGuest".equals(userName)) {
                        return userName;
                    }
                }
            }
        } catch (Exception e) {
            // Fallback to Guest if any error occurs
        }

        return "Guest";
    }

    @Override
    public String getFormattedTimestamp()
    {
        return getFormattedTimestamp(DEFAULT_TIMESTAMP_FORMAT);
    }

    @Override
    public String getFormattedTimestamp(String format)
    {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat(format);
            return sdf.format(new Date());
        } catch (Exception e) {
            // Fallback to default format if custom format fails
            SimpleDateFormat sdf = new SimpleDateFormat(DEFAULT_TIMESTAMP_FORMAT);
            return sdf.format(new Date());
        }
    }

    /**
     * Resolve a single placeholder key to its value.
     * 
     * @param key the placeholder key (without ${})
     * @return the resolved value
     */
    private String resolvePlaceholder(String key)
    {
        switch (key.toLowerCase()) {
            case "user":
                return getCurrentUser();
            case "timestamp":
                return getFormattedTimestamp();
            default:
                // Return the placeholder as-is if not recognized
                return "${" + key + "}";
        }
    }

    /**
     * Check if current request is from a mobile device.
     *
     * @return true if request is from mobile device
     */
    private boolean isMobileRequest()
    {
        try {
            XWikiContext context = getXWikiContext();
            if (context != null && context.getRequest() != null) {
                String userAgent = context.getRequest().getHeader("User-Agent");
                if (userAgent != null) {
                    return userAgent.matches("(?i).*(android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini).*");
                }
            }
        } catch (Exception e) {
            // Fallback to false if detection fails
        }
        return false;
    }

    /**
     * Get XWiki context from execution context.
     *
     * @return XWiki context or null if not available
     */
    private XWikiContext getXWikiContext()
    {
        try {
            ExecutionContext executionContext = execution.getContext();
            if (executionContext != null) {
                return (XWikiContext) executionContext.getProperty(XWikiContext.EXECUTIONCONTEXT_KEY);
            }
        } catch (Exception e) {
            // Context not available
        }
        return null;
    }

    /**
     * Escape special characters for JSON string.
     *
     * @param str the string to escape
     * @return escaped string
     */
    private String escapeJsonString(String str)
    {
        if (str == null) {
            return "";
        }

        return str.replace("\\", "\\\\")
                  .replace("\"", "\\\"")
                  .replace("\n", "\\n")
                  .replace("\r", "\\r")
                  .replace("\t", "\\t");
    }
}
