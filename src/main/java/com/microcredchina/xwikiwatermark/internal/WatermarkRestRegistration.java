package com.microcredchina.xwikiwatermark.internal;

import java.util.Collections;
import java.util.Set;

import javax.inject.Singleton;

import org.xwiki.component.annotation.Component;
import org.xwiki.rest.XWikiRestComponent;
import javax.ws.rs.core.Feature;
import javax.ws.rs.core.FeatureContext;

@Component
@Singleton
public class WatermarkRestRegistration implements XWikiRestComponent, Feature
{
    // Do not use @Override to be compatible across versions; REST server introspects methods reflectively
    public Set<Class<?>> getClasses()
    {
        return Collections.<Class<?>>singleton(WatermarkRestResource.class);
    }

    public Set<Object> getSingletons()
    {
        return Collections.emptySet();
    }

    // Jersey Feature to ensure registration even if treated as a provider
    public boolean configure(FeatureContext context)
    {
        context.register(WatermarkRestResource.class);
        return true;
    }
}


