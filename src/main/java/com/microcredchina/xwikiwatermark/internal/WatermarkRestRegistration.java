package com.microcredchina.xwikiwatermark.internal;

import java.util.Collections;
import java.util.Set;

import javax.inject.Inject;
import javax.inject.Singleton;

import org.xwiki.component.annotation.Component;
import org.xwiki.component.manager.ComponentManager;
import org.xwiki.rest.XWikiRestComponent;
import javax.ws.rs.core.Feature;
import javax.ws.rs.core.FeatureContext;

@Component
@Singleton
public class WatermarkRestRegistration implements XWikiRestComponent, Feature
{
    @Inject
    private ComponentManager componentManager;

    // Do not use @Override to be compatible across versions; REST server introspects methods reflectively
    public Set<Class<?>> getClasses()
    {
        // Return empty set - we provide instances via getSingletons()
        return Collections.emptySet();
    }

    public Set<Object> getSingletons()
    {
        try {
            // Get the WatermarkRestResource instance from XWiki component system
            WatermarkRestResource watermarkResource = componentManager.getInstance(WatermarkRestResource.class, "watermark");
            return Collections.<Object>singleton(watermarkResource);
        } catch (Exception e) {
            // If component lookup fails, return empty set
            return Collections.emptySet();
        }
    }

    // Jersey Feature to ensure registration even if treated as a provider
    public boolean configure(FeatureContext context)
    {
        // No need to register here since we provide instances via getSingletons()
        return true;
    }
}


