/*
 * See the NOTICE file distributed with this work for additional
 * information regarding copyright ownership.
 *
 * This is free software; you can redistribute it and/or modify it
 * under the terms of the GNU Lesser General Public License as
 * published by the Free Software Foundation; either version 2.1 of
 * the License, or (at your option) any later version.
 *
 * This software is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this software; if not, write to the Free
 * Software Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA
 * 02110-1301 USA, or see the FSF site: http://www.fsf.org.
 */
package com.microcredchina.xwikiwatermark;

import java.util.Map;

import org.xwiki.component.annotation.Role;

/**
 * Service for rendering watermark with placeholder resolution and parameter processing.
 * 
 * This service handles dynamic placeholder replacement and provides watermark rendering
 * parameters in JSON format for frontend consumption.
 * 
 * Supported placeholders:
 * - ${user}: Current user name (or "Guest" if not logged in)
 * - ${timestamp}: Current timestamp (configurable format)
 * 
 * @version $Id$
 * @since 1.0
 */
@Role
public interface WatermarkRenderingService
{
    /**
     * Resolve placeholders in the given template string.
     * 
     * Supported placeholders:
     * - ${user}: Replaced with current user name or "Guest"
     * - ${timestamp}: Replaced with current timestamp
     * 
     * @param template the template string containing placeholders
     * @return the resolved string with placeholders replaced
     */
    String resolveTemplate(String template);

    /**
     * Get watermark rendering configuration as a map.
     * This includes all configuration parameters needed for frontend rendering.
     * 
     * @return a map containing watermark rendering parameters
     */
    Map<String, Object> getWatermarkConfig();

    /**
     * Get watermark rendering configuration as JSON string.
     * This is convenient for direct use in JavaScript.
     * 
     * @return JSON string containing watermark rendering parameters
     */
    String getWatermarkConfigJson();

    /**
     * Get the resolved watermark text with placeholders replaced.
     * 
     * @return the final watermark text to be rendered
     */
    String getResolvedWatermarkText();

    /**
     * Check if watermark should be applied to the current request.
     * This considers configuration settings and device type.
     * 
     * @return true if watermark should be applied
     */
    boolean shouldApplyWatermark();

    /**
     * Get current user name for placeholder replacement.
     * 
     * @return current user name or "Guest" if not logged in
     */
    String getCurrentUser();

    /**
     * Get formatted timestamp for placeholder replacement.
     * 
     * @return formatted timestamp string
     */
    String getFormattedTimestamp();

    /**
     * Get formatted timestamp with custom format.
     * 
     * @param format the date format pattern
     * @return formatted timestamp string
     */
    String getFormattedTimestamp(String format);
}
