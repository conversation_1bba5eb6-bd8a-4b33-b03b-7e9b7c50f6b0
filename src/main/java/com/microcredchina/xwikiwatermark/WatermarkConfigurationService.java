/*
 * See the NOTICE file distributed with this work for additional
 * information regarding copyright ownership.
 *
 * This is free software; you can redistribute it and/or modify it
 * under the terms of the GNU Lesser General Public License as
 * published by the Free Software Foundation; either version 2.1 of
 * the License, or (at your option) any later version.
 *
 * This software is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this software; if not, write to the Free
 * Software Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA
 * 02110-1301 USA, or see the FSF site: http://www.fsf.org.
 */
package com.microcredchina.xwikiwatermark;

import java.util.Map;

import org.xwiki.component.annotation.Role;

/**
 * Service for managing watermark configuration parameters.
 * 
 * This service provides thread-safe access to watermark configuration stored in XWiki.XWikiPreferences.
 * All configuration keys are prefixed with "watermark." to avoid conflicts.
 * 
 * Supported configuration parameters:
 * - enabled: boolean - Whether watermark is enabled (default: false)
 * - textTemplate: string - Watermark text template (default: "${user} - ${timestamp}")
 * - xSpacing: integer - Horizontal spacing in pixels (default: 200)
 * - ySpacing: integer - Vertical spacing in pixels (default: 100)
 * - angle: integer - Rotation angle in degrees (default: -30)
 * - opacity: float - Transparency 0.0-1.0 (default: 0.1)
 * - fontSize: integer - Font size in pixels (default: 14)
 * - antiCopy: boolean - Enable anti-copy protection (default: false)
 * - applyToMobile: boolean - Apply to mobile devices (default: true)
 * 
 * @version $Id$
 * @since 1.0
 */
@Role
public interface WatermarkConfigurationService
{
    /**
     * Get a configuration value by key.
     * 
     * @param key the configuration key (without "watermark." prefix)
     * @return the configuration value, or default value if not set
     */
    Object getConfig(String key);

    /**
     * Set a configuration value.
     * 
     * @param key the configuration key (without "watermark." prefix)
     * @param value the configuration value
     * @throws Exception if failed to save configuration
     */
    void setConfig(String key, Object value) throws Exception;

    /**
     * Get all watermark configuration parameters.
     * 
     * @return a map containing all configuration parameters with their current values
     */
    Map<String, Object> getAllConfigs();

    /**
     * Get a boolean configuration value.
     * 
     * @param key the configuration key
     * @param defaultValue the default value if not set
     * @return the boolean value
     */
    boolean getBooleanConfig(String key, boolean defaultValue);

    /**
     * Get an integer configuration value.
     * 
     * @param key the configuration key
     * @param defaultValue the default value if not set
     * @return the integer value
     */
    int getIntConfig(String key, int defaultValue);

    /**
     * Get a float configuration value.
     * 
     * @param key the configuration key
     * @param defaultValue the default value if not set
     * @return the float value
     */
    float getFloatConfig(String key, float defaultValue);

    /**
     * Get a string configuration value.
     * 
     * @param key the configuration key
     * @param defaultValue the default value if not set
     * @return the string value
     */
    String getStringConfig(String key, String defaultValue);

    /**
     * Check if watermark is enabled.
     * 
     * @return true if watermark is enabled
     */
    boolean isEnabled();

    /**
     * Refresh the configuration cache.
     * This method should be called when configuration is updated externally.
     */
    void refreshCache();
}
