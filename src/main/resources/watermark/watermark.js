/**
 * XWiki Watermark Renderer
 * 
 * High-performance Canvas-based watermark rendering engine with mobile adaptation.
 * Supports all configuration parameters and provides fallback for older browsers.
 * 
 * @version 1.0
 * <AUTHOR> Watermark Extension
 */

(function(window, document) {
    'use strict';

    /**
     * Watermark Renderer Class
     */
    function WatermarkRenderer() {
        this.canvas = null;
        this.ctx = null;
        this.config = null;
        this.isInitialized = false;
        this.renderTimeout = null;
        this.cachedCanvas = null;
        
        // Default configuration
        this.defaultConfig = {
            enabled: false,
            textTemplate: '${user} - ${timestamp}',
            resolvedText: 'Guest - 2025-08-11 00:53:00',
            xSpacing: 200,
            ySpacing: 100,
            angle: -30,
            opacity: 0.1,
            fontSize: 14,
            antiCopy: false,
            applyToMobile: true
        };
        
        // Bind methods
        this.init = this.init.bind(this);
        this.render = this.render.bind(this);
        this.handleResize = this.handleResize.bind(this);
        this.handleConfigUpdate = this.handleConfigUpdate.bind(this);
        this.preventContextMenu = this.preventContextMenu.bind(this);
        this.preventCopyShortcuts = this.preventCopyShortcuts.bind(this);
        this.preventDragStart = this.preventDragStart.bind(this);
    }

    /**
     * Initialize the watermark renderer
     */
    WatermarkRenderer.prototype.init = function() {
        if (this.isInitialized) {
            return;
        }

        // Check Canvas support
        if (!this.isCanvasSupported()) {
            console.warn('Canvas not supported, watermark disabled');
            return;
        }

        // Load configuration
        this.loadConfiguration()
            .then(() => {
                if (!this.config.enabled) {
                    return;
                }

                // Check mobile setting
                if (!this.config.applyToMobile && this.isMobileDevice()) {
                    return;
                }

                this.createCanvas();
                this.setupEventListeners();
                this.applyAntiCopyProtection();
                this.render();
                this.isInitialized = true;
            })
            .catch(error => {
                console.error('Failed to initialize watermark:', error);
            });
    };

    /**
     * Create and setup canvas element
     */
    WatermarkRenderer.prototype.createCanvas = function() {
        this.canvas = document.createElement('canvas');
        this.canvas.id = 'xwiki-watermark-canvas';
        this.canvas.style.cssText = [
            'position: fixed',
            'top: 0',
            'left: 0',
            'width: 100%',
            'height: 100%',
            'pointer-events: none',
            'z-index: 1000',
            'opacity: ' + this.config.opacity
        ].join(';');

        this.ctx = this.canvas.getContext('2d');
        document.body.appendChild(this.canvas);
        
        this.updateCanvasSize();
    };

    /**
     * Update canvas size to match viewport
     */
    WatermarkRenderer.prototype.updateCanvasSize = function() {
        if (!this.canvas) return;
        
        const dpr = window.devicePixelRatio || 1;
        const rect = document.documentElement.getBoundingClientRect();
        
        this.canvas.width = rect.width * dpr;
        this.canvas.height = rect.height * dpr;
        
        this.canvas.style.width = rect.width + 'px';
        this.canvas.style.height = rect.height + 'px';
        
        this.ctx.scale(dpr, dpr);
    };

    /**
     * Calculate watermark positions based on spacing configuration
     */
    WatermarkRenderer.prototype.calculatePositions = function() {
        const positions = [];
        const canvasWidth = this.canvas.width / (window.devicePixelRatio || 1);
        const canvasHeight = this.canvas.height / (window.devicePixelRatio || 1);
        
        const xSpacing = this.config.xSpacing;
        const ySpacing = this.config.ySpacing;
        
        // Calculate number of watermarks needed
        const cols = Math.ceil(canvasWidth / xSpacing) + 1;
        const rows = Math.ceil(canvasHeight / ySpacing) + 1;
        
        for (let row = 0; row < rows; row++) {
            for (let col = 0; col < cols; col++) {
                positions.push({
                    x: col * xSpacing,
                    y: row * ySpacing
                });
            }
        }
        
        return positions;
    };

    /**
     * Apply text styles to canvas context
     */
    WatermarkRenderer.prototype.applyStyles = function() {
        const fontSize = this.isMobileDevice() ? 
            Math.max(this.config.fontSize * 0.8, 10) : 
            this.config.fontSize;
            
        this.ctx.font = fontSize + 'px Arial, sans-serif';
        this.ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
        this.ctx.textAlign = 'center';
        this.ctx.textBaseline = 'middle';
    };

    /**
     * Render watermark on canvas
     */
    WatermarkRenderer.prototype.render = function() {
        if (!this.canvas || !this.ctx || !this.config.enabled) {
            return;
        }

        // Clear canvas
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        
        // Apply styles
        this.applyStyles();
        
        // Calculate positions
        const positions = this.calculatePositions();
        const text = this.config.resolvedText || this.config.textTemplate;
        const angle = this.config.angle * Math.PI / 180;
        
        // Render watermarks
        positions.forEach(pos => {
            this.ctx.save();
            this.ctx.translate(pos.x, pos.y);
            this.ctx.rotate(angle);
            this.ctx.fillText(text, 0, 0);
            this.ctx.restore();
        });
    };

    /**
     * Setup event listeners
     */
    WatermarkRenderer.prototype.setupEventListeners = function() {
        // Handle window resize with debouncing
        window.addEventListener('resize', this.debounce(this.handleResize, 250));
        
        // Handle orientation change on mobile
        window.addEventListener('orientationchange', this.debounce(this.handleResize, 500));
        
        // Handle configuration updates (if needed)
        document.addEventListener('watermarkConfigUpdate', this.handleConfigUpdate);
    };

    /**
     * Handle window resize
     */
    WatermarkRenderer.prototype.handleResize = function() {
        if (!this.canvas) return;
        
        this.updateCanvasSize();
        this.render();
    };

    /**
     * Apply or remove anti-copy protection based on configuration
     */
    WatermarkRenderer.prototype.applyAntiCopyProtection = function() {
        const body = document.body;
        const protectedClass = 'xwiki-watermark-protected';

        if (this.config && this.config.antiCopy) {
            // Add anti-copy protection
            if (!body.classList.contains(protectedClass)) {
                body.classList.add(protectedClass);

                // Disable context menu
                document.addEventListener('contextmenu', this.preventContextMenu);

                // Disable common copy shortcuts
                document.addEventListener('keydown', this.preventCopyShortcuts);

                // Disable drag and drop
                document.addEventListener('dragstart', this.preventDragStart);
            }
        } else {
            // Remove anti-copy protection
            if (body.classList.contains(protectedClass)) {
                body.classList.remove(protectedClass);

                // Re-enable context menu
                document.removeEventListener('contextmenu', this.preventContextMenu);

                // Re-enable copy shortcuts
                document.removeEventListener('keydown', this.preventCopyShortcuts);

                // Re-enable drag and drop
                document.removeEventListener('dragstart', this.preventDragStart);
            }
        }
    };

    /**
     * Prevent context menu when anti-copy is enabled
     */
    WatermarkRenderer.prototype.preventContextMenu = function(e) {
        e.preventDefault();
        return false;
    };

    /**
     * Prevent copy shortcuts when anti-copy is enabled
     */
    WatermarkRenderer.prototype.preventCopyShortcuts = function(e) {
        // Prevent Ctrl+C, Ctrl+A, Ctrl+S, Ctrl+P, F12
        if (e.ctrlKey && (e.keyCode === 67 || e.keyCode === 65 || e.keyCode === 83 || e.keyCode === 80)) {
            e.preventDefault();
            return false;
        }
        // Prevent F12 (Developer Tools)
        if (e.keyCode === 123) {
            e.preventDefault();
            return false;
        }
    };

    /**
     * Prevent drag start when anti-copy is enabled
     */
    WatermarkRenderer.prototype.preventDragStart = function(e) {
        e.preventDefault();
        return false;
    };

    /**
     * Handle configuration updates
     */
    WatermarkRenderer.prototype.handleConfigUpdate = function(event) {
        if (event.detail && event.detail.config) {
            this.config = Object.assign({}, this.defaultConfig, event.detail.config);

            if (this.config.enabled) {
                if (!this.canvas) {
                    this.createCanvas();
                }
                this.canvas.style.opacity = this.config.opacity;
                this.applyAntiCopyProtection();
                this.render();
            } else if (this.canvas) {
                this.canvas.style.display = 'none';
                this.applyAntiCopyProtection(); // Remove protection when disabled
            }
        }
    };

    /**
     * Load configuration from server
     */
    WatermarkRenderer.prototype.loadConfiguration = function() {
        return new Promise((resolve, reject) => {
            // First check if watermark should be applied
            var wiki = (window.XWiki && XWiki.currentWiki) ? XWiki.currentWiki : 'xwiki';
            var base = (window.XWiki && XWiki.contextPath ? XWiki.contextPath : '') + '/rest/wikis/' + wiki + '/watermark';
            this.makeAjaxRequest(base + '/status')
                .then(response => {
                    if (!response.shouldApply) {
                        this.config = Object.assign({}, this.defaultConfig, { enabled: false });
                        resolve(this.config);
                        return;
                    }

                    // Load full configuration
                    return this.makeAjaxRequest(base + '/config');
                })
                .then(configResponse => {
                    if (configResponse) {
                        this.config = Object.assign({}, this.defaultConfig, configResponse);

                        // Load current user info for placeholder resolution
                        return this.makeAjaxRequest(base + '/user');
                    }
                })
                .then(userResponse => {
                    if (userResponse && this.config) {
                        // Update resolved text with current user info
                        let resolvedText = this.config.textTemplate || this.defaultConfig.textTemplate;
                        resolvedText = resolvedText.replace('${user}', userResponse.user || 'Guest');
                        resolvedText = resolvedText.replace('${timestamp}', userResponse.timestamp || new Date().toLocaleString());
                        this.config.resolvedText = resolvedText;
                    }

                    resolve(this.config || Object.assign({}, this.defaultConfig));
                })
                .catch(error => {
                    console.warn('Failed to load watermark configuration, using defaults:', error);
                    this.config = Object.assign({}, this.defaultConfig);
                    resolve(this.config);
                });
        });
    };

    /**
     * Check if Canvas is supported
     */
    WatermarkRenderer.prototype.isCanvasSupported = function() {
        const canvas = document.createElement('canvas');
        return !!(canvas.getContext && canvas.getContext('2d'));
    };

    /**
     * Detect mobile device
     */
    WatermarkRenderer.prototype.isMobileDevice = function() {
        return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
               (window.innerWidth <= 768);
    };

    /**
     * Make AJAX request to XWiki REST API
     */
    WatermarkRenderer.prototype.makeAjaxRequest = function(url) {
        return new Promise((resolve, reject) => {
            const xhr = new XMLHttpRequest();
            xhr.open('GET', url, true);
            xhr.setRequestHeader('Accept', 'application/json');

            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    if (xhr.status === 200) {
                        try {
                            const response = JSON.parse(xhr.responseText);
                            resolve(response);
                        } catch (e) {
                            reject(new Error('Invalid JSON response: ' + e.message));
                        }
                    } else {
                        reject(new Error('HTTP ' + xhr.status + ': ' + xhr.statusText));
                    }
                }
            };

            xhr.onerror = function() {
                reject(new Error('Network error'));
            };

            xhr.timeout = 5000; // 5 second timeout
            xhr.ontimeout = function() {
                reject(new Error('Request timeout'));
            };

            xhr.send();
        });
    };

    /**
     * Debounce function for performance optimization
     */
    WatermarkRenderer.prototype.debounce = function(func, wait) {
        let timeout;
        return function executedFunction() {
            const later = () => {
                clearTimeout(timeout);
                func.apply(this, arguments);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    };

    /**
     * Destroy watermark renderer
     */
    WatermarkRenderer.prototype.destroy = function() {
        if (this.canvas && this.canvas.parentNode) {
            this.canvas.parentNode.removeChild(this.canvas);
        }

        // Remove event listeners
        window.removeEventListener('resize', this.handleResize);
        window.removeEventListener('orientationchange', this.handleResize);
        document.removeEventListener('watermarkConfigUpdate', this.handleConfigUpdate);

        // Remove anti-copy protection
        this.config = Object.assign({}, this.defaultConfig, { antiCopy: false });
        this.applyAntiCopyProtection();

        this.canvas = null;
        this.ctx = null;
        this.isInitialized = false;
    };

    // Global instance
    window.XWikiWatermark = new WatermarkRenderer();

    // Auto-initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', window.XWikiWatermark.init);
    } else {
        // DOM is already ready
        setTimeout(window.XWikiWatermark.init, 0);
    }

})(window, document);
