/**
 * XWiki Watermark Styles
 * 
 * CSS styles for watermark display, anti-copy protection, and mobile adaptation.
 * Provides responsive design and configurable protection features.
 * 
 * @version 1.0
 * <AUTHOR> Watermark Extension
 */

/* ========================================
   Watermark Canvas Styles
   ======================================== */

#xwiki-watermark-canvas {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    pointer-events: none !important;
    z-index: 100 !important;
    opacity: 0.1;
    background: transparent !important;
    border: none !important;
    margin: 0 !important;
    padding: 0 !important;
    box-shadow: none !important;
    transform: none !important;
    transition: opacity 0.3s ease;
}

/* Ensure watermark doesn't interfere with page content */
#xwiki-watermark-canvas:hover {
    opacity: 0.05;
}

/* ========================================
   Anti-Copy Protection Styles
   ======================================== */

/* Base anti-copy protection (applied when enabled) */
.xwiki-watermark-protected {
    /* Disable text selection */
    -webkit-user-select: none !important;
    -moz-user-select: none !important;
    -ms-user-select: none !important;
    user-select: none !important;
    
    /* Disable drag and drop */
    -webkit-user-drag: none !important;
    -khtml-user-drag: none !important;
    -moz-user-drag: none !important;
    -o-user-drag: none !important;
    user-drag: none !important;
    
    /* Disable image dragging */
    -webkit-touch-callout: none !important;
    -webkit-tap-highlight-color: transparent !important;
}

/* Disable context menu when anti-copy is enabled */
.xwiki-watermark-protected * {
    -webkit-user-select: none !important;
    -moz-user-select: none !important;
    -ms-user-select: none !important;
    user-select: none !important;
}

/* Allow form inputs to remain selectable */
.xwiki-watermark-protected input,
.xwiki-watermark-protected textarea,
.xwiki-watermark-protected select,
.xwiki-watermark-protected [contenteditable="true"],
.xwiki-watermark-protected .xwiki-watermark-selectable {
    -webkit-user-select: text !important;
    -moz-user-select: text !important;
    -ms-user-select: text !important;
    user-select: text !important;
}

/* ========================================
   Mobile Device Adaptations
   ======================================== */

/* Tablet styles (768px - 1024px) */
@media screen and (max-width: 1024px) and (min-width: 769px) {
    #xwiki-watermark-canvas {
        opacity: 0.08;
    }
}

/* Mobile styles (up to 768px) */
@media screen and (max-width: 768px) {
    #xwiki-watermark-canvas {
        opacity: 0.06;
        z-index: 99;
    }
    
    /* Reduce watermark visibility on small screens */
    #xwiki-watermark-canvas:hover {
        opacity: 0.03;
    }
}

/* Small mobile styles (up to 480px) */
@media screen and (max-width: 480px) {
    #xwiki-watermark-canvas {
        opacity: 0.05;
    }
}

/* Portrait orientation adjustments */
@media screen and (orientation: portrait) {
    #xwiki-watermark-canvas {
        /* Slightly reduce opacity in portrait mode for better readability */
        opacity: 0.08;
    }
}

/* Landscape orientation adjustments */
@media screen and (orientation: landscape) and (max-width: 768px) {
    #xwiki-watermark-canvas {
        /* Maintain visibility in landscape mobile */
        opacity: 0.07;
    }
}

/* ========================================
   High DPI Display Support
   ======================================== */

/* Retina and high-DPI displays */
@media screen and (-webkit-min-device-pixel-ratio: 2),
       screen and (min-resolution: 192dpi),
       screen and (min-resolution: 2dppx) {
    #xwiki-watermark-canvas {
        /* Slightly increase opacity for better visibility on high-DPI screens */
        opacity: 0.12;
    }
}

/* ========================================
   Print Styles
   ======================================== */

@media print {
    #xwiki-watermark-canvas {
        /* Show watermark in print with higher opacity */
        opacity: 0.3 !important;
        position: absolute !important;
        z-index: 500 !important;
    }
    
    /* Ensure anti-copy protection doesn't affect printing */
    .xwiki-watermark-protected {
        -webkit-user-select: auto !important;
        -moz-user-select: auto !important;
        -ms-user-select: auto !important;
        user-select: auto !important;
    }
}

/* ========================================
   Accessibility Considerations
   ======================================== */

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
    #xwiki-watermark-canvas {
        transition: none !important;
    }
}

/* High contrast mode adjustments */
@media (prefers-contrast: high) {
    #xwiki-watermark-canvas {
        opacity: 0.15;
    }
}

/* ========================================
   Browser-Specific Fixes
   ======================================== */

/* Internet Explorer compatibility */
.ie #xwiki-watermark-canvas {
    filter: alpha(opacity=10);
}

/* Safari-specific adjustments */
@supports (-webkit-appearance: none) {
    #xwiki-watermark-canvas {
        -webkit-backface-visibility: hidden;
        -webkit-transform: translateZ(0);
    }
}

/* Firefox-specific adjustments */
@-moz-document url-prefix() {
    #xwiki-watermark-canvas {
        -moz-backface-visibility: hidden;
    }
}

/* ========================================
   Performance Optimizations
   ======================================== */

/* GPU acceleration for better performance */
#xwiki-watermark-canvas {
    will-change: opacity;
    transform: translateZ(0);
    -webkit-transform: translateZ(0);
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
}

/* ========================================
   Utility Classes
   ======================================== */

/* Class to temporarily hide watermark */
.xwiki-watermark-hidden #xwiki-watermark-canvas {
    display: none !important;
}

/* Class to make content selectable even with anti-copy enabled */
.xwiki-watermark-selectable {
    -webkit-user-select: text !important;
    -moz-user-select: text !important;
    -ms-user-select: text !important;
    user-select: text !important;
}

/* Class to disable watermark on specific elements */
.xwiki-watermark-disabled {
    position: relative;
    z-index: 101;
    background: white;
}

/* Ensure XWiki UI elements are above watermark */
.xwiki-menu, .xwiki-drawer, .xwiki-modal, .xwiki-popup, .xwiki-tooltip {
    z-index: 1000 !important;
}

/* ========================================
   Animation Classes (Optional)
   ======================================== */

/* Fade-in animation for watermark */
@keyframes watermarkFadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 0.1;
    }
}

#xwiki-watermark-canvas.fade-in {
    animation: watermarkFadeIn 1s ease-in-out;
}

/* Pulse animation for emphasis (can be triggered via JavaScript) */
@keyframes watermarkPulse {
    0%, 100% {
        opacity: 0.1;
    }
    50% {
        opacity: 0.2;
    }
}

#xwiki-watermark-canvas.pulse {
    animation: watermarkPulse 2s ease-in-out infinite;
}
