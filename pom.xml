<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.microcredchina.xwikiwatermark</groupId>
    <artifactId>xwiki-watermark</artifactId>
    <version>1.0-SNAPSHOT</version>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <xwiki.version>17.4.3</xwiki.version>
    </properties>



    <dependencies>
        <!-- XWiki核心依赖 -->
        <dependency>
            <groupId>org.xwiki.commons</groupId>
            <artifactId>xwiki-commons-component-api</artifactId>
            <version>${xwiki.version}</version>
            <scope>provided</scope>
        </dependency>

        <!-- XWiki配置API -->
        <dependency>
            <groupId>org.xwiki.commons</groupId>
            <artifactId>xwiki-commons-configuration-api</artifactId>
            <version>${xwiki.version}</version>
            <scope>provided</scope>
        </dependency>

        <!-- XWiki执行上下文 -->
        <dependency>
            <groupId>org.xwiki.commons</groupId>
            <artifactId>xwiki-commons-context</artifactId>
            <version>${xwiki.version}</version>
            <scope>provided</scope>
        </dependency>

        <!-- XWiki管理API -->
        <dependency>
            <groupId>org.xwiki.platform</groupId>
            <artifactId>xwiki-platform-administration-api</artifactId>
            <version>${xwiki.version}</version>
            <scope>provided</scope>
        </dependency>

        <!-- XWiki平台核心 -->
        <dependency>
            <groupId>org.xwiki.platform</groupId>
            <artifactId>xwiki-platform-oldcore</artifactId>
            <version>${xwiki.version}</version>
            <scope>provided</scope>
        </dependency>

        <!-- XWiki REST API -->
        <dependency>
            <groupId>org.xwiki.platform</groupId>
            <artifactId>xwiki-platform-rest-api</artifactId>
            <version>${xwiki.version}</version>
            <scope>provided</scope>
        </dependency>
        <!-- XWiki REST Server (for registering custom JAX-RS components) -->
        <dependency>
            <groupId>org.xwiki.platform</groupId>
            <artifactId>xwiki-platform-rest-server</artifactId>
            <version>${xwiki.version}</version>
            <scope>provided</scope>
        </dependency>

        <!-- XWiki UI Extension API -->
        <dependency>
            <groupId>org.xwiki.platform</groupId>
            <artifactId>xwiki-platform-uiextension-api</artifactId>
            <version>${xwiki.version}</version>
            <scope>provided</scope>
        </dependency>

        <!-- 测试依赖 -->
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>4.13.2</version>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <!-- Maven编译插件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.11.0</version>
                <configuration>
                    <source>17</source>
                    <target>17</target>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>

            <!-- Maven JAR插件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>3.3.0</version>
                <configuration>
                    <archive>
                        <manifest>
                            <addDefaultImplementationEntries>true</addDefaultImplementationEntries>
                            <addDefaultSpecificationEntries>true</addDefaultSpecificationEntries>
                        </manifest>
                    </archive>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <repositories>
        <repository>
            <id>xwiki-releases</id>
            <name>XWiki Releases</name>
            <url>https://nexus.xwiki.org/nexus/content/groups/public</url>
        </repository>
    </repositories>
</project>