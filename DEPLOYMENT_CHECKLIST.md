# XWiki水印扩展 - 部署检查清单

## 部署前准备

### ✅ 文件准备
- [ ] 确认JAR文件存在：`target/xwiki-watermark-1.0-SNAPSHOT.jar`
- [ ] 验证JAR文件大小：约33KB
- [ ] 备份现有XWiki配置（如有）

### ✅ 环境检查
- [ ] XWiki版本：17.4.3或更高版本
- [ ] Java版本：17或更高版本
- [ ] 确认XWiki服务可以正常停止和启动

## 部署步骤

### 1. 停止XWiki服务
```bash
# 根据您的部署方式选择相应命令
sudo systemctl stop xwiki
# 或
sudo service xwiki stop
# 或停止Tomcat/Jetty等应用服务器
```

### 2. 部署JAR文件
```bash
# 复制JAR文件到XWiki的lib目录
cp target/xwiki-watermark-1.0-SNAPSHOT.jar /path/to/xwiki/WEB-INF/lib/

# 设置正确的文件权限
chmod 644 /path/to/xwiki/WEB-INF/lib/xwiki-watermark-1.0-SNAPSHOT.jar
```

### 3. 启动XWiki服务
```bash
# 启动XWiki服务
sudo systemctl start xwiki
# 或
sudo service xwiki start
```

### 4. 验证部署

#### 4.1 检查启动日志
```bash
# 查看XWiki启动日志，确认以下信息：
tail -f /path/to/xwiki/logs/xwiki.log

# 期望看到的日志信息：
# "XWiki application ready, initializing Watermark Extension..."
# "XWiki Watermark Extension initialization completed successfully"
```

#### 4.2 测试REST API
```bash
# 测试状态端点
curl -H "Accept: application/json" \
     http://localhost:8080/rest/wikis/xwiki/watermark/status

# 期望响应：HTTP 200 + JSON格式数据
# {"shouldApply": true} 或 {"shouldApply": false}
```

#### 4.3 检查管理面板
- [ ] 访问：`http://localhost:8080/bin/admin/XWiki/XWikiPreferences`
- [ ] 在左侧导航栏查找"水印配置"（中文）或"Watermark Settings"（英文）
- [ ] 确认配置页面可以正常打开

#### 4.4 测试前端功能
- [ ] 启用水印配置
- [ ] 访问任意XWiki页面
- [ ] 确认水印在页面上正常显示
- [ ] 检查浏览器控制台无错误信息

## 故障排除

### 如果启动失败
1. 检查XWiki日志中的详细错误信息
2. 确认JAR文件没有损坏
3. 验证文件权限设置正确
4. 检查XWiki版本兼容性

### 如果REST API返回500错误
1. 确认组件是否正确加载
2. 检查依赖注入相关错误
3. 验证XWiki上下文是否可用

### 如果管理面板不显示
1. 检查国际化资源文件是否正确加载
2. 确认ConfigurableClass对象是否创建成功
3. 验证用户权限设置

### 如果水印不显示
1. 检查水印配置是否启用
2. 验证JavaScript是否正确加载
3. 确认Canvas是否支持
4. 检查移动端设置

## 配置建议

### 推荐的初始配置
```
启用水印: true
水印文字模板: ${user} - ${timestamp}
水平间距: 200px
垂直间距: 100px
旋转角度: -30度
透明度: 0.1
字体大小: 14px
防复制保护: false
移动端显示: true
```

### 性能优化建议
- 在高流量环境中，考虑调整水印间距以减少渲染负载
- 监控REST API响应时间
- 定期检查配置缓存性能

## 回滚计划

如果部署出现问题，可以按以下步骤回滚：

1. **停止XWiki服务**
2. **移除JAR文件**：
   ```bash
   rm /path/to/xwiki/WEB-INF/lib/xwiki-watermark-1.0-SNAPSHOT.jar
   ```
3. **重启XWiki服务**
4. **验证系统恢复正常**

## 部署完成确认

- [ ] XWiki服务正常启动
- [ ] 组件加载日志正常
- [ ] REST API响应正常
- [ ] 管理面板显示正确
- [ ] 前端水印功能正常
- [ ] 国际化显示正确
- [ ] 无错误日志产生

**部署完成时间**: ___________  
**部署人员**: ___________  
**验证人员**: ___________
